package com.gw.user.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
class UserActiveServiceTest {

    @Autowired
    private UserActiveService userActiveService;

    @Autowired
    private CacheManager cacheManager;

    @Test
    void testRecordUserActive() {
        String username = "testuser_" + System.currentTimeMillis();

        // 第一次记录应该成功
        boolean result1 = userActiveService.recordUserActive(username);
        assertTrue(result1, "第一次记录用户活跃应该成功");

        // 同一天再次记录应该返回false（已存在，应该命中缓存）
        boolean result2 = userActiveService.recordUserActive(username);
        assertFalse(result2, "同一天重复记录用户活跃应该返回false（缓存命中）");

        // 第三次记录，验证缓存一致性
        boolean result3 = userActiveService.recordUserActive(username);
        assertFalse(result3, "第三次记录应该仍然返回false（缓存命中）");
    }

    @Test
    void testRecordUserActiveWithDate() {
        String username = "testuser2";
        LocalDate testDate = LocalDate.of(2024, 1, 1);
        
        // 指定日期记录应该成功
        boolean result = userActiveService.recordUserActive(username, testDate);
        assertTrue(result, "指定日期记录用户活跃应该成功");
    }

    @Test
    void testCountTodayActiveUsers() {
        // 记录一些用户活跃
        userActiveService.recordUserActive("user1");
        userActiveService.recordUserActive("user2");
        userActiveService.recordUserActive("user3");
        
        int count = userActiveService.countTodayActiveUsers();
        assertTrue(count >= 3, "今日活跃用户数应该至少为3");
    }

    @Test
    void testCountActiveUsersByDate() {
        LocalDate testDate = LocalDate.of(2024, 2, 1);
        userActiveService.recordUserActive("dateuser1", testDate);
        userActiveService.recordUserActive("dateuser2", testDate);
        
        int count = userActiveService.countActiveUsersByDate(testDate);
        assertTrue(count >= 2, "指定日期活跃用户数应该至少为2");
    }

    @Test
    void testCountCurrentMonthActiveUsers() {
        int count = userActiveService.countCurrentMonthActiveUsers();
        assertTrue(count >= 0, "当月活跃用户数应该大于等于0");
    }

    @Test
    void testCountCurrentYearActiveUsers() {
        int count = userActiveService.countCurrentYearActiveUsers();
        assertTrue(count >= 0, "当年活跃用户数应该大于等于0");
    }

    @Test
    void testCountActiveUsersInPast30Days() {
        List<Map<String, Object>> result = userActiveService.countActiveUsersInPast30Days();
        assertNotNull(result, "过去30天活跃用户统计结果不应为null");
    }

    @Test
    void testCountActiveUsersByDateRange() {
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        
        List<Map<String, Object>> result = userActiveService.countActiveUsersByDateRange(startDate, endDate);
        assertNotNull(result, "日期范围活跃用户统计结果不应为null");
    }

    @Test
    void testInvalidParameters() {
        // 测试空用户名
        boolean result = userActiveService.recordUserActive("");
        assertFalse(result, "空用户名应该返回false");

        // 测试null用户名
        boolean result2 = userActiveService.recordUserActive(null);
        assertFalse(result2, "null用户名应该返回false");

        // 测试无效日期范围
        assertThrows(IllegalArgumentException.class, () -> {
            userActiveService.countActiveUsersByDateRange(LocalDate.now(), LocalDate.now().minusDays(1));
        }, "开始日期晚于结束日期应该抛出异常");
    }

    @Test
    void testCachePerformance() {
        String username = "cachetest_" + System.currentTimeMillis();
        LocalDate testDate = LocalDate.now();

        // 第一次记录，会查询数据库
        long start1 = System.currentTimeMillis();
        boolean result1 = userActiveService.recordUserActive(username, testDate);
        long time1 = System.currentTimeMillis() - start1;
        assertTrue(result1, "第一次记录应该成功");

        // 第二次记录，应该命中缓存，速度更快
        long start2 = System.currentTimeMillis();
        boolean result2 = userActiveService.recordUserActive(username, testDate);
        long time2 = System.currentTimeMillis() - start2;
        assertFalse(result2, "第二次记录应该返回false（缓存命中）");

        // 缓存命中应该比数据库查询快（这个断言可能在测试环境中不稳定，仅作参考）
        System.out.println("第一次查询时间: " + time1 + "ms, 第二次查询时间（缓存）: " + time2 + "ms");

        // 测试统计方法的缓存
        long start3 = System.currentTimeMillis();
        int count1 = userActiveService.countActiveUsersByDate(testDate);
        long time3 = System.currentTimeMillis() - start3;

        long start4 = System.currentTimeMillis();
        int count2 = userActiveService.countActiveUsersByDate(testDate);
        long time4 = System.currentTimeMillis() - start4;

        assertEquals(count1, count2, "两次统计结果应该相同");
        System.out.println("第一次统计时间: " + time3 + "ms, 第二次统计时间（缓存）: " + time4 + "ms");
    }

    @Test
    void testCacheManagerDirectly() {
        String username = "cachemanager_test_" + System.currentTimeMillis();
        LocalDate testDate = LocalDate.now();

        // 验证缓存管理器存在
        assertNotNull(cacheManager, "CacheManager应该被注入");

        // 验证缓存实例存在
        assertNotNull(cacheManager.getCache("userActive"), "userActive缓存应该存在");
        assertNotNull(cacheManager.getCache("userActiveStats"), "userActiveStats缓存应该存在");

        // 第一次记录，应该成功
        boolean result1 = userActiveService.recordUserActive(username, testDate);
        assertTrue(result1, "第一次记录应该成功");

        // 验证缓存中确实有数据
        String cacheKey = username + ":" + testDate.toString();
        var cache = cacheManager.getCache("userActive");
        var cachedValue = cache.get(cacheKey);
        assertNotNull(cachedValue, "缓存中应该有数据");
        assertEquals(Boolean.TRUE, cachedValue.get(), "缓存的值应该是true");

        // 第二次记录，应该命中缓存返回false
        boolean result2 = userActiveService.recordUserActive(username, testDate);
        assertFalse(result2, "第二次记录应该返回false（缓存命中）");

        // 清除缓存后再次记录，应该查询数据库
        cache.evict(cacheKey);
        boolean result3 = userActiveService.recordUserActive(username, testDate);
        assertFalse(result3, "清除缓存后再次记录应该返回false（数据库查询）");

        // 验证缓存又被重新填充
        cachedValue = cache.get(cacheKey);
        assertNotNull(cachedValue, "缓存应该被重新填充");
        assertEquals(Boolean.TRUE, cachedValue.get(), "重新填充的缓存值应该是true");
    }
}
