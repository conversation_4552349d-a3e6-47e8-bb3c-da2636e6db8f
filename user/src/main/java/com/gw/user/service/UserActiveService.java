package com.gw.user.service;

import com.gw.user.entity.UserActiveEntity;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户活跃服务接口
 */
public interface UserActiveService {

    /**
     * 记录用户活跃（如果当日没有记录则创建）
     *
     * @param username 用户名
     * @return 是否成功创建新记录
     */
    boolean recordUserActive(String username);

    /**
     * 记录用户在指定日期的活跃（如果该日期没有记录则创建）
     *
     * @param username   用户名
     * @param activeDate 活跃日期
     * @return 是否成功创建新记录
     */
    boolean recordUserActive(String username, LocalDate activeDate);

    /**
     * 统计当日活跃用户数
     *
     * @return 当日活跃用户数
     */
    int countTodayActiveUsers();

    /**
     * 统计指定日期的活跃用户数
     *
     * @param date 日期
     * @return 活跃用户数
     */
    int countActiveUsersByDate(LocalDate date);

    /**
     * 统计当月活跃用户数
     *
     * @return 当月活跃用户数
     */
    int countCurrentMonthActiveUsers();

    /**
     * 统计指定月份的活跃用户数
     *
     * @param year  年份
     * @param month 月份
     * @return 活跃用户数
     */
    int countActiveUsersByMonth(int year, int month);

    /**
     * 统计当年活跃用户数
     *
     * @return 当年活跃用户数
     */
    int countCurrentYearActiveUsers();

    /**
     * 统计指定年份的活跃用户数
     *
     * @param year 年份
     * @return 活跃用户数
     */
    int countActiveUsersByYear(int year);

    /**
     * 统计过去指定天数内每天的活跃用户数
     *
     * @param days 天数
     * @return 每天的活跃用户数统计，包含日期和活跃用户数
     */
    List<Map<String, Object>> countActiveUsersInPastDays(int days);

    /**
     * 统计过去30天每天的活跃用户数
     *
     * @return 每天的活跃用户数统计
     */
    List<Map<String, Object>> countActiveUsersInPast30Days();

    /**
     * 统计指定日期范围内每天的活跃用户数
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 每天的活跃用户数统计
     */
    List<Map<String, Object>> countActiveUsersByDateRange(LocalDate startDate, LocalDate endDate);

    /**
     * 统计指定时间范围内的活跃用户数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 活跃用户数
     */
    int countActiveUsersByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
}
