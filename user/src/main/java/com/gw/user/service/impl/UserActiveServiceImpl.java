package com.gw.user.service.impl;

import com.gw.user.constant.UserActiveCacheConstant;
import com.gw.user.entity.UserActiveEntity;
import com.gw.user.mapper.UserActiveMapper;
import com.gw.user.service.UserActiveService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.cache.Cache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 用户活跃服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserActiveServiceImpl implements UserActiveService {

    private final UserActiveMapper userActiveMapper;
    private final CacheManager cacheManager;
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordUserActive(String username) {
        return recordUserActive(username, LocalDate.now());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordUserActive(String username, LocalDate activeDate) {
        if (username == null || username.trim().isEmpty()) {
            log.warn("用户名为空，无法记录用户活跃");
            return false;
        }

        if (activeDate == null) {
            activeDate = LocalDate.now();
        }

        String cacheKey = UserActiveCacheConstant.generateUserActiveCacheKey(username, activeDate.toString());

        // 先检查缓存中是否已存在
        Boolean cachedExists = getCachedUserActiveRecord(cacheKey);
        if (Boolean.TRUE.equals(cachedExists)) {
            log.debug("用户 {} 在 {} 的活跃记录已存在（缓存命中）", username, activeDate);
            return false;
        }

        // 检查数据库是否已存在记录
        int existingCount = userActiveMapper.countByUsernameAndDate(username, activeDate);
        if (existingCount > 0) {
            log.debug("用户 {} 在 {} 的活跃记录已存在（数据库查询）", username, activeDate);
            // 将存在的记录放入缓存
            putUserActiveRecordToCache(cacheKey, true);
            return false;
        }

        // 创建新地活跃记录
        UserActiveEntity entity = new UserActiveEntity();
        entity.setUsername(username);
        entity.setActiveDate(activeDate);

        int result = userActiveMapper.insert(entity);
        if (result > 0) {
            log.info("成功记录用户 {} 在 {} 的活跃", username, activeDate);
            // 将新记录放入缓存
            putUserActiveRecordToCache(cacheKey, true);
            // 清除统计缓存
            evictStatsCache();
            return true;
        } else {
            log.error("记录用户 {} 在 {} 的活跃失败", username, activeDate);
            return false;
        }
    }

    /**
     * 从缓存获取用户活跃记录
     */
    private Boolean getCachedUserActiveRecord(String cacheKey) {
        Cache cache = cacheManager.getCache(UserActiveCacheConstant.USER_ACTIVE_CACHE);
        if (cache != null) {
            Cache.ValueWrapper wrapper = cache.get(cacheKey);
            if (wrapper != null) {
                return (Boolean) wrapper.get();
            }
        }
        return null;
    }

    /**
     * 将用户活跃记录放入缓存
     */
    private void putUserActiveRecordToCache(String cacheKey, Boolean exists) {
        Cache cache = cacheManager.getCache(UserActiveCacheConstant.USER_ACTIVE_CACHE);
        if (cache != null) {
            cache.put(cacheKey, exists);
        }
    }

    /**
     * 清除统计缓存
     */
    private void evictStatsCache() {
        Cache cache = cacheManager.getCache(UserActiveCacheConstant.USER_ACTIVE_STATS_CACHE);
        if (cache != null) {
            cache.clear();
        }
    }

    @Override
    public int countTodayActiveUsers() {
        return countActiveUsersByDate(LocalDate.now());
    }

    @Override
    public int countActiveUsersByDate(LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }

        String cacheKey = UserActiveCacheConstant.generateDateActiveCountCacheKey(date.toString());
        LocalDate finalDate = date;
        return getCachedStatsOrCompute(cacheKey, () -> userActiveMapper.countActiveUsersByDate(finalDate));
    }

    @Override
    public int countCurrentMonthActiveUsers() {
        LocalDate now = LocalDate.now();
        return countActiveUsersByMonth(now.getYear(), now.getMonthValue());
    }

    @Override
    public int countActiveUsersByMonth(int year, int month) {
        String cacheKey = UserActiveCacheConstant.generateMonthActiveCountCacheKey(year, month);
        return getCachedStatsOrCompute(cacheKey, () -> userActiveMapper.countActiveUsersByMonth(year, month));
    }

    @Override
    public int countCurrentYearActiveUsers() {
        return countActiveUsersByYear(LocalDate.now().getYear());
    }

    @Override
    public int countActiveUsersByYear(int year) {
        String cacheKey = UserActiveCacheConstant.generateYearActiveCountCacheKey(year);
        return getCachedStatsOrCompute(cacheKey, () -> userActiveMapper.countActiveUsersByYear(year));
    }

    /**
     * 获取缓存的统计数据，如果不存在则计算并缓存
     */
    private int getCachedStatsOrCompute(String cacheKey, java.util.function.Supplier<Integer> supplier) {
        Cache cache = cacheManager.getCache(UserActiveCacheConstant.USER_ACTIVE_STATS_CACHE);
        if (cache != null) {
            Cache.ValueWrapper wrapper = cache.get(cacheKey);
            if (wrapper != null) {
                Integer cachedValue = (Integer) wrapper.get();
                if (cachedValue != null) {
                    log.debug("统计缓存命中: key={}, value={}", cacheKey, cachedValue);
                    return cachedValue;
                }
            }
        }

        // 缓存未命中，执行计算
        int result = supplier.get();

        // 将结果放入缓存
        if (cache != null) {
            cache.put(cacheKey, result);
            log.debug("统计结果已缓存: key={}, value={}", cacheKey, result);
        }

        return result;
    }

    @Override
    public List<Map<String, Object>> countActiveUsersInPastDays(int days) {
        if (days <= 0) {
            days = 30;
        }
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);
        return countActiveUsersByDateRange(startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> countActiveUsersInPast30Days() {
        return countActiveUsersInPastDays(30);
    }

    @Override
    public List<Map<String, Object>> countActiveUsersByDateRange(LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null) {
            throw new IllegalArgumentException("开始日期和结束日期不能为空");
        }
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }
        return userActiveMapper.countActiveUsersByDateRange(startDate, endDate);
    }

    @Override
    public int countActiveUsersByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            throw new IllegalArgumentException("开始时间和结束时间不能为空");
        }
        if (startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }
        return userActiveMapper.countActiveUsersByTimeRange(startTime, endTime);
    }
}
